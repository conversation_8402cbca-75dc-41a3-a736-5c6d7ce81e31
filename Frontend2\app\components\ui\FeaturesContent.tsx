import { POPUP_STYLES } from "~/lib/utils";
import { FEATURES } from "~/lib/types";

interface FeaturesContentProps {
  onClose: () => void;
}

export function FeaturesContent({ onClose }: FeaturesContentProps) {
  return (
    <div className="w-full text-left">
      <h2 className={POPUP_STYLES.title}>
        Our Features
      </h2>
      
      <div className="flex flex-col gap-6 my-8">
        {FEATURES.map((feature, index) => (
          <div 
            key={index}
            className="p-6 rounded-xl bg-white/60 border border-law-gold/30 transition-all duration-300 hover:bg-white/90 hover:border-law-gold/50 hover:-translate-y-2 hover:shadow-lg shadow-md"
          >
            <h3 className="bg-gold-texture bg-clip-text text-transparent font-merriweather font-bold text-xl leading-tight mb-4 bg-cover bg-center">
              {feature.title}
            </h3>
            <p className="text-sm text-gray-700 leading-relaxed m-0 font-source-sans-pro">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
      
      <button 
        className={POPUP_STYLES.button}
        onClick={onClose}
      >
        Close
      </button>
    </div>
  );
}

export default FeaturesContent;
