import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Email validation utility
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Form validation utilities
export function validateRequired(value: string, fieldName: string): string | null {
  if (!value || value.trim() === '') {
    return `${fieldName} is required`;
  }
  return null;
}

export function validateEmail(email: string): string | null {
  if (!email || email.trim() === '') {
    return 'Email is required';
  }
  if (!isValidEmail(email)) {
    return 'Please provide a valid email address';
  }
  return null;
}

export function validatePhone(phone: string): string | null {
  if (!phone || phone.trim() === '') {
    return 'Phone number is required';
  }
  // Basic phone validation - can be enhanced based on requirements
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return 'Please provide a valid phone number';
  }
  return null;
}

// Rating validation for feedback form
export function validateRating(rating: string, fieldName: string): string | null {
  if (!rating || rating.trim() === '') {
    return `${fieldName} rating is required`;
  }
  const ratingNum = parseInt(rating);
  if (isNaN(ratingNum) || ratingNum < 1 || ratingNum > 5) {
    return `${fieldName} rating must be between 1 and 5`;
  }
  return null;
}

// Conditional field validation for low ratings
export function validateConditionalField(
  rating: string, 
  conditionalValue: string, 
  fieldName: string
): string | null {
  const ratingNum = parseInt(rating);
  if (ratingNum && ratingNum < 3 && (!conditionalValue || conditionalValue.trim() === '')) {
    return `Please explain what you didn't like for ${fieldName} ratings below 3`;
  }
  return null;
}

// Debounce utility for form inputs
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Format date utility
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
}

// Sanitize input utility
export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

// Generate unique ID utility
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Local storage utilities
export const storage = {
  get: (key: string) => {
    if (typeof window === 'undefined') return null;
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch {
      return null;
    }
  },
  
  set: (key: string, value: any) => {
    if (typeof window === 'undefined') return;
    try {
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch {
      // Handle storage errors silently
    }
  },
  
  remove: (key: string) => {
    if (typeof window === 'undefined') return;
    try {
      window.localStorage.removeItem(key);
    } catch {
      // Handle storage errors silently
    }
  }
};

// Constants for styling
export const POPUP_STYLES = {
  overlay: "fixed inset-0 bg-black/60 flex items-center justify-center z-50 backdrop-blur-md p-4",
  container: "bg-law-cream rounded-2xl p-6 sm:p-8 md:p-10 max-w-lg w-full max-h-[90vh] sm:max-h-[80vh] overflow-y-auto popup-scroll relative shadow-2xl border border-law-gold/20",
  containerBorder: "before:absolute before:inset-[-2px] before:bg-gradient-to-br before:from-law-gold/30 before:to-law-gold/10 before:rounded-2xl before:z-[-1]",
  close: "absolute top-4 right-4 bg-none border-none text-2xl cursor-pointer text-gray-500 hover:text-gray-800 transition-colors font-montserrat",
  title: "bg-gold-texture bg-clip-text text-transparent font-merriweather font-bold text-2xl sm:text-3xl mb-6 sm:mb-8 text-center bg-cover bg-center",
  form: "flex flex-col gap-5 sm:gap-6",
  formGroup: "flex flex-col gap-2.5",
  label: "font-montserrat font-semibold text-sm sm:text-base text-gray-800 tracking-wide",
  input: "p-3 sm:p-4 border border-gray-300 rounded-lg text-sm sm:text-base bg-white transition-all focus:outline-none focus:border-law-gold focus:ring-2 focus:ring-law-gold/20 font-source-sans-pro",
  textarea: "p-3 sm:p-4 border border-gray-300 rounded-lg text-sm sm:text-base bg-white transition-all focus:outline-none focus:border-law-gold focus:ring-2 focus:ring-law-gold/20 resize-vertical min-h-[100px] font-source-sans-pro",
  select: "p-3 sm:p-4 border border-gray-300 rounded-lg text-sm sm:text-base bg-white transition-all focus:outline-none focus:border-law-gold focus:ring-2 focus:ring-law-gold/20 font-source-sans-pro",
  button: "p-4 sm:p-5 border-none rounded-lg bg-gradient-to-r from-law-gold to-law-gold/90 text-black font-montserrat font-semibold text-base sm:text-lg cursor-pointer transition-all hover:scale-105 hover:shadow-lg mt-4 shadow-md",
  error: "bg-red-50 text-red-800 p-4 rounded-lg border border-red-200 text-sm mb-5 text-center font-source-sans-pro"
};

export const BUTTON_STYLES = {
  primary: "flex w-64 h-12 px-6 items-center justify-center rounded-lg border-none bg-gradient-to-r from-law-gold to-law-gold/90 text-black font-montserrat font-semibold text-lg cursor-pointer transition-all hover:scale-105 hover:shadow-lg shadow-md",
  secondary: "flex w-64 h-12 px-6 items-center justify-center rounded-lg border-2 border-law-gold bg-transparent text-law-gold font-montserrat font-semibold text-lg cursor-pointer transition-all hover:bg-law-gold hover:text-black hover:scale-105",
  disabled: "opacity-60 cursor-not-allowed transform-none"
};


